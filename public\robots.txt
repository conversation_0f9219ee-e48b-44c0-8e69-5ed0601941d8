# StreamDB.online - Robots.txt
# This file controls how search engines crawl and index our site
# Admin panel and sensitive areas are completely blocked from all crawlers

# Block ALL bots from admin panel and related paths
User-agent: *
Disallow: /admin
Disallow: /admin/
Disallow: /admin/*
Disallow: /login
Disallow: /reset-password
Disallow: /_admin
Disallow: /api/admin
Disallow: /dashboard
Disallow: /management

# Allow all other content for major search engines
User-agent: Googlebot
Allow: /
Disallow: /admin
Disallow: /admin/
Disallow: /admin/*
Disallow: /login
Disallow: /reset-password

User-agent: Bingbot
Allow: /
Disallow: /admin
Disallow: /admin/
Disallow: /admin/*
Disallow: /login
Disallow: /reset-password

User-agent: Twitterbot
Allow: /
Disallow: /admin
Disallow: /admin/
Disallow: /admin/*

User-agent: facebookexternalhit
Allow: /
Disallow: /admin
Disallow: /admin/
Disallow: /admin/*

# Block aggressive crawlers and scrapers
User-agent: AhrefsBot
Disallow: /

User-agent: MJ12bot
Disallow: /

User-agent: DotBot
Disallow: /

User-agent: SemrushBot
Disallow: /

# Crawl delay for respectful bots
Crawl-delay: 1

# Sitemap location
Sitemap: https://streamdb.online/sitemap.xml
